#!/usr/bin/env python3
"""
Quick test script to verify column ordering implementation.
"""

import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'flatmate', 'src'))

from fm.core.data_services.standards.columns import Columns
from fm.core.data_services.standards.column_order import StandardColumnsOrder

def test_column_ordering():
    """Test that columns are properly ordered."""
    print("=== Column Order Test ===\n")
    
    # Test 1: Get all columns in order
    print("1. All columns in FM-standard order:")
    ordered_columns = Columns.get_ordered_columns()
    for i, col in enumerate(ordered_columns[:10]):  # Show first 10
        print(f"   {i+1:2d}. {col.display_name:15} (order: {col.order:3d}, db: {col.db_name})")
    print(f"   ... and {len(ordered_columns)-10} more columns\n")
    
    # Test 2: Get core transaction columns in order
    print("2. Core transaction columns in order:")
    core_columns = Columns.get_ordered_columns('core_transaction_cols')
    for i, col in enumerate(core_columns):
        print(f"   {i+1}. {col.display_name:15} (order: {col.order:3d})")
    print()
    
    # Test 3: Test StandardColumnsOrder enum
    print("3. StandardColumnsOrder enum values:")
    order_items = list(StandardColumnsOrder)[:10]  # First 10
    for item in order_items:
        print(f"   {item.name:15} = {item.value}")
    print()
    
    # Test 4: Verify order values are applied correctly
    print("4. Verify specific columns have correct order:")
    test_columns = [Columns.DATE, Columns.DETAILS, Columns.AMOUNT, Columns.CATEGORY]
    for col in test_columns:
        expected_order = StandardColumnsOrder[col.db_name.upper()].value
        print(f"   {col.display_name:15}: expected {expected_order:3d}, actual {col.order:3d} {'✓' if col.order == expected_order else '✗'}")
    print()
    
    # Test 5: Test sorting functionality
    print("5. Test column sorting:")
    unsorted_cols = [Columns.CATEGORY, Columns.DATE, Columns.AMOUNT, Columns.DETAILS]
    print("   Before sorting:", [col.display_name for col in unsorted_cols])
    sorted_cols = Columns.sort_columns_by_order(unsorted_cols)
    print("   After sorting: ", [col.display_name for col in sorted_cols])
    print()
    
    print("=== Test Complete ===")

if __name__ == "__main__":
    test_column_ordering()
