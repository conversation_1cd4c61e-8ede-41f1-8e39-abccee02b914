from typing import List, Any
from .column_definition import Column
from .column_order import StandardColumnsOrder
from .column_order_service import ColumnOrderService



class Columns:
    """
    A central, type-safe registry for all column definitions in the application.

    This class replaces the old StandardColumns enum and various column group lists.
    It provides a single source of truth for column metadata (db_name, display_name, dtype)
    and their logical groupings.

    Usage:
        - Get a specific column:      `Columns.DATE`
        - Get a column's db_name:    `Columns.DATE.db_name`
        - Get a group of columns:     `Columns.get(G_STATEMENT_HANDLER)`
    """
    # --- Core Transaction Identifiers ---
    DATE = Column(db_name='date', display_name='Date', dtype=str, groups=['statement_handler', 'core_transaction_cols'], width=12, order=StandardColumnsOrder.DATE.value)
    DETAILS = Column(db_name='details', display_name='Details', dtype=str, groups=['statement_handler', 'core_transaction_cols'], width=40, order=StandardColumnsOrder.DETAILS.value)
    AMOUNT = Column(db_name='amount', display_name='Amount', dtype=float, groups=['statement_handler', 'core_transaction_cols'], width=12, order=StandardColumnsOrder.AMOUNT.value)
    BALANCE = Column(db_name='balance', display_name='Balance', dtype=float, groups=['statement_handler', 'core_transaction_cols'], width=12, order=StandardColumnsOrder.BALANCE.value)
    ACCOUNT = Column(db_name='account', display_name='Account', dtype=str, groups=['statement_handler', 'core_transaction_cols'], width=20, order=StandardColumnsOrder.ACCOUNT.value)

    # --- Statement Handling Specific ---
    # Unique identification (separated for data integrity)
    SOURCE_UID = Column(db_name='source_uid', display_name='Source UID', dtype=str, groups=['statement_handler'], order=StandardColumnsOrder.SOURCE_UID.value)  # Bank-provided unique ID
    UNIQUE_ID = Column(db_name='unique_id', display_name='Unique Id', dtype=str, groups=['statement_handler'], order=StandardColumnsOrder.UNIQUE_ID.value)  # Legacy - maps to SOURCE_UID
    EMPTY_COLUMN = Column(db_name='empty', display_name='Empty', dtype=Any, groups=['statement_handler'], order=StandardColumnsOrder.EMPTY_COLUMN.value)
    CREDIT_AMOUNT = Column(db_name='credit_amount', display_name='Credit', dtype=float, groups=['statement_handler'], order=StandardColumnsOrder.CREDIT_AMOUNT.value)
    DEBIT_AMOUNT = Column(db_name='debit_amount', display_name='Debit', dtype=float, groups=['statement_handler'], order=StandardColumnsOrder.DEBIT_AMOUNT.value)
    PAYMENT_TYPE = Column(db_name='payment_type', display_name='Payment Type', dtype=str, groups=['statement_handler'], order=StandardColumnsOrder.PAYMENT_TYPE.value)
    TP_REF = Column(db_name='tp_ref', display_name='TP Ref', dtype=str, groups=['statement_handler'], order=StandardColumnsOrder.TP_REF.value)
    TP_PART = Column(db_name='tp_part', display_name='TP Part', dtype=str, groups=['statement_handler'], order=StandardColumnsOrder.TP_PART.value)
    TP_CODE = Column(db_name='tp_code', display_name='TP Code', dtype=str, groups=['statement_handler'], order=StandardColumnsOrder.TP_CODE.value)
    OP_REF = Column(db_name='op_ref', display_name='OP Ref', dtype=str, groups=['statement_handler'], order=StandardColumnsOrder.OP_REF.value)
    OP_PART = Column(db_name='op_part', display_name='OP Part', dtype=str, groups=['statement_handler'], order=StandardColumnsOrder.OP_PART.value)
    OP_CODE = Column(db_name='op_code', display_name='OP Code', dtype=str, groups=['statement_handler'], order=StandardColumnsOrder.OP_CODE.value)
    OP_NAME = Column(db_name='op_name', display_name='OP Name', dtype=str, groups=['statement_handler'], order=StandardColumnsOrder.OP_NAME.value)
    OP_ACCOUNT = Column(db_name='op_account', display_name='OP Account', dtype=str, groups=['statement_handler'], order=StandardColumnsOrder.OP_ACCOUNT.value)

    ## ---Statement Handler MetaData ---
    SOURCE_FILE = Column(db_name='source_filename', display_name='Source Filename', dtype=str, groups=['statement_handler'], order=StandardColumnsOrder.SOURCE_FILENAME.value)
    SOURCE_BANK = Column(db_name='source_bank', display_name='Source Bank', dtype=str, groups=['statement_handler'], order=StandardColumnsOrder.SOURCE_BANK.value)
    SOURCE_TYPE = Column(db_name='source_type', display_name='Statement Type', dtype=str, groups=['statement_handler'], order=StandardColumnsOrder.SOURCE_TYPE.value)
    STATEMENT_DATE = Column(db_name='statement_date', display_name='Statement Date', dtype=str, groups=['statement_handler'], order=StandardColumnsOrder.STATEMENT_DATE.value)

    # --- User-Editable Fields ---
    CATEGORY = Column(db_name='category', display_name='Category', dtype=str, groups=['user_editable'], width=20, order=StandardColumnsOrder.CATEGORY.value)
    TAGS = Column(db_name='tags', display_name='Tags', dtype=str, groups=['user_editable'], width=30, order=StandardColumnsOrder.TAGS.value)
    NOTES = Column(db_name='notes', display_name='Notes', dtype=str, groups=['user_editable'], width=40, order=StandardColumnsOrder.NOTES.value)

    # ---Database System Columns (not for default display or direct user editing) ---can be displayed when selected
    IMPORT_DATE = Column(db_name='import_date', display_name='Import Date', dtype=str, groups=['db_system'], order=StandardColumnsOrder.IMPORT_DATE.value)
    MODIFIED_DATE = Column(db_name='modified_date', display_name='Modified Date', dtype=str, groups=['db_system'], order=StandardColumnsOrder.MODIFIED_DATE.value)
    DB_UID = Column(db_name='db_uid', display_name='DB UID', dtype=str, groups=['db_system'], order=StandardColumnsOrder.DB_UID.value)  # Generated hash for duplicate detection
    HASH = Column(db_name='hash', display_name='Hash', dtype=str, groups=['db_system'], order=StandardColumnsOrder.HASH.value)  # Legacy - use DB_UID instead

    # --- Internal Methods for Registry Access ---
    @classmethod
    def get_all_columns(cls) -> List[Column]:
        """Returns a list of all Column objects defined in this class."""
        return [getattr(cls, attr) for attr in dir(cls) if isinstance(getattr(cls, attr), Column)]

    @classmethod
    def get_transaction_columns(cls) -> List[Column]:
        """Returns all columns needed for the Transaction table, including db_system columns."""
        # Transaction table needs ALL columns - statement data, user data, and system data
        return cls.get_all_columns()

    @classmethod
    def get(cls, group_name: str) -> List[Column]:
        """Returns a list of all columns belonging to the specified group."""
        return [col for col in cls.get_all_columns() if group_name in col.groups]

    @classmethod
    def from_db_name(cls, db_name: str):
        """Returns a Column object from its database name."""
        for col in cls.get_all_columns():
            if col.db_name == db_name:
                return col
        return None

    @classmethod
    def from_display_name(cls, display_name: str):
        """Returns a Column object from its display name."""
        for col in cls.get_all_columns():
            if col.display_name == display_name:
                return col
        return None

    # --- COLUMN ORDERING METHODS ---

    @classmethod
    def get_ordered_columns(cls, group_name: str = None, module_name: str = None) -> List[Column]:
        """
        Get columns in proper FM-standard order with user preferences applied.

        Args:
            group_name: Optional group filter (e.g., 'core_transaction_cols')
            module_name: Optional module name for user preferences (future use)

        Returns:
            List of Column objects sorted by FM-standard order
        """
        if group_name:
            columns = cls.get(group_name)
        else:
            columns = cls.get_all_columns()

        # Sort by order field (lower values first)
        return sorted(columns, key=lambda col: col.order)

    @classmethod
    def get_default_order(cls) -> List[Column]:
        """Get all columns in FM-standard order."""
        return cls.get_ordered_columns()

    @classmethod
    def sort_columns_by_order(cls, columns: List[Column]) -> List[Column]:
        """Sort a list of columns according to FM-standard order."""
        return sorted(columns, key=lambda col: col.order)

    @classmethod
    def get_ordered_display_columns(cls, module_name: str = None) -> List[Column]:
        """
        Get display columns in proper order with user preferences applied.

        Args:
            module_name: Module name for user preferences (e.g., 'categorize')

        Returns:
            List of display columns sorted by user preference or FM-standard order
        """
        # Get display columns (core + user_editable)
        display_cols = cls.get_display_columns()

        # If no module specified, just use FM-standard order
        if not module_name:
            return cls.sort_columns_by_order(display_cols)

        # Use ColumnOrderService for user preferences
        order_service = ColumnOrderService()
        ordered_db_names = order_service.get_column_order(module_name)

        # Create mapping of db_name to Column object
        col_map = {col.db_name: col for col in display_cols}

        # Build ordered list, including only columns that exist in display_cols
        ordered_cols = []
        for db_name in ordered_db_names:
            if db_name in col_map:
                ordered_cols.append(col_map[db_name])

        # Add any display columns not in the order (shouldn't happen, but safety)
        for col in display_cols:
            if col not in ordered_cols:
                ordered_cols.append(col)

        return ordered_cols

    # --- CONSOLIDATED METHODS (from ColumnManager and ColumnNameService) ---

    @classmethod
    def get_display_columns(cls) -> List[Column]:
        """Returns columns for UI display (core_transaction + user_editable)."""
        core = cls.get('core_transaction')
        user = cls.get('user_editable')
        return list(dict.fromkeys(core + user))

    @classmethod
    def get_display_mapping(cls, db_names: List[str]) -> dict[str, str]:
        """Returns a db_name -> display_name mapping for the given db_names."""
        mapping = {}
        for name in db_names:
            col = cls.from_db_name(name)
            if col:
                mapping[name] = col.display_name
            else:
                mapping[name] = name.replace('_', ' ').title()  # Fallback
        return mapping

    @classmethod
    def get_reverse_display_mapping(cls, display_names: List[str]) -> dict[str, str]:
        """Returns a display_name -> db_name mapping for the given display_names."""
        mapping = {}
        for name in display_names:
            col = cls.from_display_name(name)
            if col:
                mapping[name] = col.db_name
            else:
                mapping[name] = name.replace(' ', '_').lower() # Fallback
        return mapping

    @classmethod
    def apply_display_names_to_df(cls, df, custom_mapping=None):
        """Converts a DataFrame's column names from db_names to display_names.
        
        Args:
            df: DataFrame with db_names as columns
            custom_mapping: Optional dictionary of {db_name: display_name} mappings
                         to override the standard display names
        """
        if df.empty:
            return df
            
        # Get standard mapping first
        mapping = cls.get_display_mapping(df.columns.tolist())
        
        # Apply any custom mappings if provided
        if custom_mapping:
            mapping = {**mapping, **custom_mapping}
            
        return df.rename(columns=mapping)

    @classmethod
    def get_column_widths(cls) -> dict[str, int]:
        """Returns a mapping of display_name -> width for all columns."""
        return {col.display_name: col.width for col in cls.get_all_columns()}

    @classmethod
    def get_default_visible_columns(cls, module_name: str) -> List[str]:
        """Returns a list of default visible column db_names for a given module in proper order."""
        if module_name == 'categorize':
            # Define categorize-specific columns in FM-standard order
            categorize_columns = [
                cls.DATE,      # order: 1
                cls.DETAILS,   # order: 2
                cls.AMOUNT,    # order: 3
                cls.ACCOUNT,   # order: 4
                cls.CATEGORY,  # order: 11
                cls.TAGS,      # order: 12
                cls.NOTES,     # order: 13
            ]
            # Sort by order and return db_names
            sorted_cols = cls.sort_columns_by_order(categorize_columns)
            return [col.db_name for col in sorted_cols]

        # Fallback for other modules - core transaction columns in order
        core_columns = [cls.DATE, cls.DETAILS, cls.AMOUNT, cls.ACCOUNT]
        sorted_cols = cls.sort_columns_by_order(core_columns)
        return [col.db_name for col in sorted_cols]
