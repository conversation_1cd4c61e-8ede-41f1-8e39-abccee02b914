# Feature Development Guide

## Overview
This document defines the standard pattern for developing features in the FlatMate application, with a focus on database operations and service layers. This pattern ensures consistency, maintainability, and testability across all features.

## Core Principles

### 1. Separation of Concerns
- **Configuration**: Define runtime behavior and defaults
- **Data Management**: Handle data loading, transformation, and persistence
- **Business Logic**: Implement core functionality
- **Presentation**: Handle UI rendering (if applicable)
- **State Management**: Manage feature state and lifecycle

### 2. Consistent API Surface
- Standardized method naming and behavior
- Clear public interface vs. internal implementation
- Type hints and docstrings for all public methods

### 3. Lifecycle Management
- Clear initialization and cleanup
- Resource management
- State restoration

## Standard Feature Structure

### Base Feature Template
```python
class BaseFeature:
    """Base class for all feature implementations."""
    
    def __init__(self, config: Optional[Dict] = None):
        """Initialize with optional configuration.
        
        Args:
            config: Feature-specific configuration
        """
        self._config = self._get_default_config()
        if config:
            self.configure(**config)
        self._is_initialized = False
        self._setup()
    
    # === CONFIGURATION ===
    @classmethod
    def _get_default_config(cls) -> Dict:
        """Return default configuration for this feature."""
        return {
            'enabled': True,
            'log_level': 'INFO',
        }
    
    def configure(self, **kwargs) -> 'BaseFeature':
        """Update feature configuration.
        
        Args:
            **kwargs: Configuration options to update
            
        Returns:
            self for method chaining
        """
        for key, value in kwargs.items():
            if key in self._config:
                self._config[key] = value
            else:
                raise ValueError(f"Unknown configuration option: {key}")
        return self
    
    # === LIFECYCLE ===
    def initialize(self) -> 'BaseFeature':
        """Initialize feature resources."""
        if not self._is_initialized:
            self._initialize()
            self._is_initialized = True
        return self
    
    def cleanup(self) -> None:
        """Clean up resources."""
        if self._is_initialized:
            self._cleanup()
            self._is_initialized = False
    
    # === CORE FUNCTIONALITY ===
    def execute(self, *args, **kwargs):
        """Execute the main feature logic."""
        if not self._is_initialized:
            self.initialize()
        return self._execute(*args, **kwargs)
    
    # === INTERNAL METHODS ===
    def _setup(self) -> None:
        """Set up internal state and resources."""
        pass
    
    def _initialize(self) -> None:
        """Initialize any required resources."""
        pass
    
    def _cleanup(self) -> None:
        """Clean up any allocated resources."""
        pass
    
    def _execute(self, *args, **kwargs):
        """Internal implementation of the feature logic."""
        raise NotImplementedError("Subclasses must implement _execute")
```

## Database Feature Example

### Database Exporter Feature
```python
class DatabaseExporter(BaseFeature):
    """Feature for exporting database content to various formats."""
    
    @classmethod
    def _get_default_config(cls) -> Dict:
        """Return default configuration for database export."""
        return {
            **super()._get_default_config(),
            'output_format': 'csv',
            'include_headers': True,
            'encoding': 'utf-8',
            'batch_size': 1000,
        }
    
    def _initialize(self) -> None:
        """Initialize database connection and resources."""
        self._db = DatabaseConnection()
        self._logger = logging.getLogger(__name__)
    
    def _cleanup(self) -> None:
        """Clean up database resources."""
        if hasattr(self, '_db'):
            self._db.close()
    
    def _execute(self, query: str, output_path: str, **kwargs) -> None:
        """Execute database export."""
        config = {**self._config, **kwargs}
        self._logger.info(f"Starting export to {output_path}")
        
        with self._db.cursor() as cursor:
            cursor.execute(query)
            
            if config['output_format'] == 'csv':
                self._export_to_csv(cursor, output_path, config)
            elif config['output_format'] == 'json':
                self._export_to_json(cursor, output_path, config)
            else:
                raise ValueError(f"Unsupported format: {config['output_format']}")
        
        self._logger.info(f"Export completed: {output_path}")
    
    def _export_to_csv(self, cursor, output_path: str, config: Dict) -> None:
        """Export query results to CSV."""
        with open(output_path, 'w', encoding=config['encoding'], newline='') as f:
            writer = csv.writer(f)
            
            if config['include_headers']:
                writer.writerow([desc[0] for desc in cursor.description])
            
            while True:
                rows = cursor.fetchmany(config['batch_size'])
                if not rows:
                    break
                writer.writerows(rows)
    
    def _export_to_json(self, cursor, output_path: str, config: Dict) -> None:
        """Export query results to JSON."""
        columns = [desc[0] for desc in cursor.description]
        with open(output_path, 'w', encoding=config['encoding']) as f:
            batch = []
            while True:
                rows = cursor.fetchmany(config['batch_size'])
                if not rows:
                    break
                batch.extend(dict(zip(columns, row)) for row in rows)
            json.dump(batch, f, indent=2)
```

## Integration with UI Components

For features with UI components, combine with the widget pattern:

```python
class DatabaseExportWidget(QWidget):
    """UI for database export feature."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._exporter = DatabaseExporter()
        self._setup_ui()
    
    def _setup_ui(self):
        # Setup UI components
        self.format_combo = QComboBox()
        self.format_combo.addItems(['csv', 'json'])
        
        self.export_btn = QPushButton("Export")
        self.export_btn.clicked.connect(self._on_export)
        
        # Layout setup...
    
    def _on_export(self):
        try:
            self._exporter.execute(
                query="SELECT * FROM transactions",
                output_path="export.csv",
                output_format=self.format_combo.currentText()
            )
            QMessageBox.information(self, "Success", "Export completed successfully")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Export failed: {str(e)}")
```

## Best Practices

1. **Configuration Management**
   - Use `_get_default_config()` for default values
   - Validate configuration in `configure()`
   - Document all configuration options

2. **Error Handling**
   - Handle expected errors gracefully
   - Log detailed error information
   - Provide user-friendly error messages

3. **Resource Management**
   - Use context managers for resources
   - Implement proper cleanup in `_cleanup()`
   - Handle initialization failures gracefully

4. **Testing**
   - Write unit tests for business logic
   - Mock external dependencies
   - Test error conditions

5. **Documentation**
   - Document public APIs with docstrings
   - Include examples in documentation
   - Document configuration options

## Naming Conventions

### Classes
- `PascalCase` for class names
- `Base` prefix for abstract base classes (e.g., `BaseFeature`)
- `Feature` suffix for feature implementations (e.g., `DatabaseExporter`)
- `Widget` suffix for UI components (e.g., `DatabaseExportWidget`)

### Methods
- `verb_noun()` for public methods
- `_verb_noun()` for protected/internal methods
- `get_`/`set_` for property accessors
- `is_`/`has_` for boolean properties

### Variables
- `snake_case` for variables and parameters
- `_prefix` for protected attributes
- `UPPER_CASE` for constants

## Example Directory Structure

```
feature_name/
├── __init__.py
├── feature.py        # Feature implementation
├── widget.py         # UI components (if any)
├── models.py         # Data models
├── utils.py          # Helper functions
└── tests/            # Unit tests
    ├── __init__.py
    ├── test_feature.py
    └── test_widget.py
```

## Versioning and Backward Compatibility

1. **Versioning**
   - Use semantic versioning for features
   - Document breaking changes
   - Provide migration guides when needed

2. **Backward Compatibility**
   - Deprecate features before removal
   - Provide clear upgrade paths
   - Maintain changelog

## Performance Considerations

1. **Resource Usage**
   - Use generators for large datasets
   - Implement batching for bulk operations
   - Cache expensive operations when appropriate

2. **Memory Management**
   - Release resources promptly
   - Use weak references where appropriate
   - Monitor memory usage

## Security Considerations

1. **Input Validation**
   - Validate all external inputs
   - Use parameterized queries for database operations
   - Sanitize outputs

2. **Authentication & Authorization**
   - Implement proper access controls
   - Validate permissions
   - Log security-relevant events

## Related Documents

- [Widget Pattern Guide](./APP_WIDE_WIDGET_PATTERN.md)
- [Coding Standards](../CODING_STANDARDS.md)
- [Testing Guidelines](../TESTING.md)
