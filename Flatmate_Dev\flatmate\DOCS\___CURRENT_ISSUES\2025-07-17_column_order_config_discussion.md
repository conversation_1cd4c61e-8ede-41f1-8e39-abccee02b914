# Column Order Configuration – Discussion

## Summary
- **Default column order** should be set sensibly at the application-wide level.
- **Last used column order** should be stored as a module-specific user preference.
- Both should be defined and managed centrally in a `Columns` config structure.

## Issues
- Current behaviour may not consistently respect user preferences or sensible defaults.
- Lack of a unified approach can cause confusion and inconsistent UI states across modules.

## Proposed Actions
- Audit existing column order logic and storage.
- Define a clear schema for `Columns` config (default vs. per-module/user).
- Ensure all modules reference this config for both initial load and preference updates.

---
*Document created: 2025-07-17 to track and resolve column order config issues.*

## Proposed Solution

**Problem:**
Column order in tables and dropdowns defaults to alphabetical, not FM standards. The old enum (now archived) defined the correct order, but the current dataclass-based system lost this.

**Proposal:**
- Reintroduce a canonical column order by creating a new `StandardColumnsOrder` enum, modelled on the archived enum.
- Reference this enum in the `Column` dataclass (e.g., via an `order` or `standard_enum` field).
- Update the `Columns` registry and all UI/table/dropdown logic to use this enum for ordering, not alphabetical or dict order.
- This restores FM standards, ensures developer clarity, and enables type-safe, explicit ordering.

**Pros:**
- Explicit, maintainable, and type-safe.
- Centralised—easy to update and audit.
- Developer- and user-friendly.

**Cons:**
- Requires code changes in column registry and UI logic.
- Slight increase in boilerplate (enum + dataclass reference).

**Actionable Plan:**
1. Define the new enum in the standards module.
2. Update the `Column` dataclass to reference the enum.
3. Update the `Columns` registry and all relevant logic to use the enum order.
4. Test in all affected modules.

---

## Alternative Options Considered

### 1. Explicit List in Columns Registry (No Enum)
Maintain a single, ordered list (e.g. `STANDARD_COLUMN_ORDER = [Columns.DATE, Columns.DETAILS, ...]`) in the registry. UI and logic reference this list for canonical order.

**Pros:**
- Simple, no extra indirection.
- Easy to update.

**Cons:**
- No type safety; risk of accidental omission or duplication.
- Less discoverable for developers.

### 2. Order Field on Column Dataclass
Add an `order: int` field to each `Column` instance. Sort columns by this field for display.

**Pros:**
- No need for enum or external list.
- Order is intrinsic to each column.

**Cons:**
- Harder to maintain across many columns (risk of duplicate/missing numbers).
- Not as readable as a list or enum.

### 3. YAML/JSON Config for Order
Define order in an external config file (YAML/JSON). Load and apply at runtime.

**Pros:**
- Non-code maintainers can update order.
- Decouples order from code.

**Cons:**
- More moving parts (sync issues between config and code).
- Harder to enforce correctness.

### 4. Status Quo (Alphabetical/Dict Order)
Continue with current approach.

**Pros:**
- No work required.

**Cons:**
- Not FM standard; poor UX.

---

## Comparison and Final Recommendation

| Option                       | Type Safety | Centralised | Easy to Update | Risk of Error | Developer Friendly | User Friendly |
|------------------------------|:-----------:|:-----------:|:--------------:|:-------------:|:------------------:|:-------------:|
| Enum-based (proposed)        |   High      |    High     |     High       |     Low       |       High         |    High       |
| Explicit list                |   Low       |    High     |     High       |    Medium     |       Medium       |    High       |
| Order field on dataclass     |   Medium    |   Medium    |    Medium      |    Medium     |       Medium       |    High       |
| YAML/JSON config             |   Medium    |    High     |     High       |    Medium     |       Medium       |    High       |
| Status quo                   |    Low      |    Low      |     High       |    High       |        Low         |    Low        |

**Final Recommendation:**
The enum-based approach is preferred. It provides explicit, type-safe, and centralised control over column order, is easy to maintain, and best supports both developer and user needs. The explicit list is a fallback if type safety is not a priority. Avoid the status quo and ad-hoc integer fields for long-term maintainability.

---

## User-Customised Column Order (Module-Specific)

If the user changes the column order in a module, that preference should persist and override the default order for that module.

- **Requirement:**
  - Store user-specific column order for each module (e.g. categorise, transactions, etc).
  - On load, check for and apply the user's preferred order; fall back to the FM-standard default if none exists.
  - When the user changes order, update the stored preference.

- **Implications:**
  - Implementation must support per-module, per-user order persistence (e.g. in user config, DB, or local storage).
  - UI logic should always check for a user preference before applying the default order.
  - The enum-based (or other default) system remains the canonical fallback.

This ensures users always see columns in their preferred order, while retaining a robust, FM-standard default for new users or modules.

---

## Module-Level vs. Central Config for User Column Order

**Considerations:**

- **Module-level handling:**
  - Each module (e.g. categorise, transactions) manages its own column order preference for the user.
  - *Pros:* Maximum flexibility; users can have different column orders per module.
  - *Cons:* No way to set a global preference; more config entries.

- **Central/local config system:**
  - User can set column order prefs in a central config (e.g. `user_config.yaml`, settings UI), possibly with both global and per-module overrides.
  - *Pros:* User can set a global default (applies everywhere unless overridden), but also override per module if desired. More discoverable and manageable.
  - *Cons:* Slightly more complex config logic, but more powerful.

**Recommendation:**

Adopt a hybrid approach:
- Store user column order preferences in the local config system, supporting both:
  - A global default order (applies to all modules unless overridden)
  - Per-module overrides (specific to a module)
- On load: check for per-module user preference → else global user preference → else FM-standard default.

*This approach provides maximum user control and clarity, is easy to manage via config file or settings UI, and is consistent with best-practice for user preferences.*

---

## Troubleshooting Protocol Refinement

- Add a step to explicitly document the minimal reproducible scenario in the discussion doc at the start of each troubleshooting cycle.
- Add a feedback loop for protocol improvements after each major issue is resolved.

---
