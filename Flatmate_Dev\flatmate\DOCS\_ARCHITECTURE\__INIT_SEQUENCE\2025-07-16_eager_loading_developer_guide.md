# Eager Loading Developer Guide

## Quick Start

The Flatmate application now uses an **eager loading architecture** where all modules are created at startup and navigation is instant. This guide helps developers work effectively with this system.

## Core Concepts

### 1. Module Lifecycle
```
Startup:    Create → Setup → Load Data → Hide
Navigation: Show ↔ Hide (instant)
Shutdown:   Cleanup
```

**Key Point**: Modules are created once and reused, never destroyed during navigation.

### 2. Data Loading Strategy
- **Load data during setup** (one-time cost)
- **Never load data during navigation** (must be instant)
- **Use client-side filtering** on loaded data

### 3. Widget Management
- **Avoid `deleteLater()`** - causes Qt crashes
- **Use `setParent(None)`** to remove widgets safely
- **Make setup methods idempotent** - safe to call multiple times

## Working with Modules

### Creating a New Module

**1. Create the Presenter**
```python
# my_module/my_presenter.py
from fm.modules.base.base_presenter import BasePresenter

class MyPresenter(BasePresenter):
    def __init__(self, main_window, gui_config=None, gui_keys=None):
        super().__init__(main_window, gui_config, gui_keys)
        
        # Initialize services
        self.data_service = MyDataService()
        
        # Load data during initialization (CRITICAL)
        self._load_initial_data()
    
    def _create_view(self):
        """Create view with injected dependencies."""
        return MyView(gui_config=self.gui_config, gui_keys=self.gui_keys)
    
    def _connect_signals(self):
        """Connect signals after view creation."""
        self.view.action_requested.connect(self._handle_action)
        # DO NOT load data here - do it in __init__
    
    def _load_initial_data(self):
        """Load all data needed for this module."""
        # This runs once during startup
        data = self.data_service.get_all_data()
        # Store data for later use
        self.cached_data = data
    
    def _refresh_content(self, **params):
        """Lightweight UI refresh only - must be instant."""
        # Show panels, update UI state
        # DO NOT load data here
        self.main_window.show_left_panel()
        
        # Use cached data
        if hasattr(self, 'cached_data'):
            self.view.display_data(self.cached_data)
```

**2. Create the View**
```python
# my_module/my_view.py
from fm.modules.base.base_module_view import BaseModuleView

class MyView(BaseModuleView):
    def __init__(self, parent=None, gui_config=None, gui_keys=None):
        super().__init__(parent, gui_config, gui_keys)
        # View is ready for immediate use
    
    def setup_left_panel(self, layout):
        """Set up left panel content."""
        # Create your left panel widgets
        pass
    
    def setup_center_panel(self, layout):
        """Set up center panel content."""
        # Create your main content widgets
        pass
```

**3. Register the Module**
```python
# module_coordinator.py
def initialize_modules(self):
    self.modules = {
        'my_module': MyPresenter(self.main_window, gui_config, GuiKeys),
        # ... other modules
    }
```

**4. Add Navigation (if needed)**
```python
# nav_pane.py
MODULE_TO_NAV_MAP = {
    "my_module": NAV_MY_BUTTON,
}

# module_coordinator.py  
NAV_TO_MODULE_MAP = {
    'my_button': 'my_module',
}
```

### Modifying Existing Modules

**DO**:
- Load data in `__init__()` or `_connect_signals()`
- Use cached/loaded data in `_refresh_content()`
- Make operations instant during navigation
- Use dependency injection for services

**DON'T**:
- Load data in `_refresh_content()` 
- Make database calls during navigation
- Use `deleteLater()` on widgets
- Import GUI config directly (use injection)

## Data Management Patterns

### Pattern 1: Load All Data at Startup
```python
class MyPresenter(BasePresenter):
    def __init__(self, main_window, gui_config=None, gui_keys=None):
        super().__init__(main_window, gui_config, gui_keys)
        
        # Load everything needed
        self.all_data = self.data_service.get_all_data()
        self.filtered_data = self.all_data  # Start with all data
    
    def apply_filters(self, filters):
        """Client-side filtering - instant response."""
        self.filtered_data = self.all_data[
            self.all_data['date'].between(filters['start'], filters['end'])
        ]
        self.view.update_display(self.filtered_data)
```

### Pattern 2: Progressive Data Loading
```python
class MyPresenter(BasePresenter):
    def __init__(self, main_window, gui_config=None, gui_keys=None):
        super().__init__(main_window, gui_config, gui_keys)
        
        # Load essential data immediately
        self.core_data = self.data_service.get_core_data()
        
        # Schedule additional data loading
        QTimer.singleShot(100, self._load_additional_data)
    
    def _load_additional_data(self):
        """Load non-critical data after UI is responsive."""
        self.extended_data = self.data_service.get_extended_data()
```

### Pattern 3: Cached Data with Refresh
```python
class MyPresenter(BasePresenter):
    def __init__(self, main_window, gui_config=None, gui_keys=None):
        super().__init__(main_window, gui_config, gui_keys)
        
        # Check cache first
        if cache_service.has('my_data'):
            self.data = cache_service.get('my_data')
        else:
            self.data = self.data_service.get_data()
            cache_service.set('my_data', self.data)
    
    def refresh_data(self):
        """Manual refresh when needed."""
        self.data = self.data_service.get_fresh_data()
        cache_service.set('my_data', self.data)
        self.view.update_display(self.data)
```

## Widget Lifecycle Best Practices

### Safe Widget Removal
```python
# ❌ WRONG - Causes Qt crashes
def cleanup_widgets(self):
    for widget in self.widgets:
        widget.deleteLater()  # DON'T DO THIS

# ✅ CORRECT - Safe removal
def cleanup_widgets(self):
    for widget in self.widgets:
        widget.setParent(None)  # Removes from layout safely
```

### Idempotent Setup
```python
def setup_in_main_window(self, main_window):
    """Safe to call multiple times."""
    if self._is_setup_in_window:
        # Already set up, just show
        self._add_panels_to_layouts()
        return
    
    # First time setup
    self._create_panels()
    self._is_setup_in_window = True
```

### Panel Management
```python
def _add_panels_to_layouts(self):
    """Manually add widgets to avoid Qt crashes."""
    if self.left_panel:
        # Remove from any existing layout first
        if self.left_panel.parent():
            self.left_panel.setParent(None)
        
        # Add to target layout
        self.main_window.left_layout.addWidget(self.left_panel)
        self.left_panel.show()
```

## Performance Guidelines

### Memory Management
- **Target**: <1MB per module
- **Monitor**: Use memory profiling tools
- **Optimize**: Use efficient data structures (pandas DataFrames)

### Response Time Targets
- **Navigation**: <100ms (instant feel)
- **Filtering**: <100ms (real-time feel)  
- **Data refresh**: <500ms (acceptable delay)
- **Startup**: <10s total (with loading screen)

### Threading Guidelines
```python
# For long-running operations during setup
class DataLoader(QThread):
    data_loaded = Signal(object)
    
    def run(self):
        data = expensive_data_operation()
        self.data_loaded.emit(data)

# In presenter
def _load_data_async(self):
    self.loader = DataLoader()
    self.loader.data_loaded.connect(self._on_data_loaded)
    self.loader.start()
```

## Debugging Tips

### Common Issues

**1. Slow Navigation**
- Check if data loading happens in `_refresh_content()`
- Move expensive operations to `__init__()` or `_connect_signals()`

**2. Qt Crashes**
- Look for `deleteLater()` calls
- Check widget parent/child relationships
- Ensure proper cleanup in `cleanup_from_main_window()`

**3. Memory Leaks**
- Disconnect signals in cleanup methods
- Avoid circular references
- Use weak references where appropriate

**4. Import Errors**
- Use dependency injection instead of direct imports
- Check for circular import patterns
- Use late imports if necessary

### Logging Best Practices
```python
import logging
log = logging.getLogger(__name__)

def _load_data(self):
    log.debug("Starting data load")
    start_time = time.time()
    
    data = self.data_service.get_data()
    
    elapsed = time.time() - start_time
    log.info(f"Loaded {len(data)} records in {elapsed:.2f}s")
```

## Testing Strategies

### Unit Testing Modules
```python
def test_module_creation():
    """Test module can be created without errors."""
    presenter = MyPresenter(mock_window, mock_config, mock_keys)
    assert presenter is not None
    assert hasattr(presenter, 'cached_data')

def test_navigation_speed():
    """Test navigation is instant."""
    start_time = time.time()
    presenter.show()
    elapsed = time.time() - start_time
    assert elapsed < 0.1  # Must be under 100ms
```

### Integration Testing
```python
def test_full_navigation_cycle():
    """Test complete navigation cycle without crashes."""
    coordinator = ModuleCoordinator(main_window)
    coordinator.initialize_modules()
    
    # Test navigation cycle
    coordinator.transition_to('home')
    coordinator.transition_to('my_module')
    coordinator.transition_to('home')  # Should not crash
```

## Migration Guide

### Converting Existing Modules

**Step 1**: Move data loading from `_refresh_content()` to `__init__()`
**Step 2**: Add dependency injection parameters
**Step 3**: Make `_refresh_content()` lightweight
**Step 4**: Test navigation cycles
**Step 5**: Verify memory usage

### Example Migration
```python
# BEFORE
class OldPresenter:
    def _refresh_content(self):
        data = self.service.load_data()  # ❌ Slow
        self.view.display(data)

# AFTER  
class NewPresenter(BasePresenter):
    def __init__(self, main_window, gui_config=None, gui_keys=None):
        super().__init__(main_window, gui_config, gui_keys)
        self.data = self.service.load_data()  # ✅ One-time
    
    def _refresh_content(self):
        self.view.display(self.data)  # ✅ Instant
```

## Success Checklist

For each module, verify:
- [ ] Navigation is instant (<100ms)
- [ ] No data loading in `_refresh_content()`
- [ ] Memory usage is reasonable (<1MB)
- [ ] No Qt crashes on navigation cycles
- [ ] Proper dependency injection used
- [ ] Widget cleanup is safe (no `deleteLater()`)
- [ ] Setup methods are idempotent
- [ ] Logging provides useful debugging info
