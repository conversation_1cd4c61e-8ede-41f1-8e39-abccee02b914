# Column Order Integration Issues - Discussion

## Summary
After implementing the column ordering system, several issues have been identified in the categorize module that prevent the proper display and ordering of columns.

---

## Issue 1: Only Three Columns Displayed (Category, Notes, Tags)

**Problem:**
- Only 3 columns are showing in the transaction table: Category, Notes, Tags
- These are in alphabetical order, not FM-standard order
- Expected columns (Date, Details, Amount, Account) are missing

**Expected Behavior:**
- Should display: Date, Details, Amount, Account, Category, Tags, Notes (in that order)
- Should show 7 columns total as defined in `get_default_visible_columns('categorize')`

**Probable Causes to Investigate:**

1. **get_ordered_display_columns() Logic Issue:**
   - The method may be returning only `user_editable` columns instead of all display columns
   - Integration test showed "Got 3 ordered display columns" - this suggests `get_display_columns()` is not returning the expected columns

2. **Column Group Definitions:**
   - Core transaction columns may not be in the `core_transaction` group
   - The `get_display_columns()` method combines `core_transaction` + `user_editable` groups
   - Need to verify column group assignments in `columns.py`

3. **DataFrame Column Availability:**
   - The DataFrame passed to the table may not contain the expected columns
   - Need to verify what columns are actually in the data being displayed

4. **Table View Configuration:**
   - The table view may be filtering out columns that aren't in the DataFrame
   - Default visible columns logic may be overridden somewhere

---

## Issue 2: Search Columns Dropdown Inconsistency

**Problem:**
- Search columns dropdown shows different columns than those displayed in the table
- Inconsistency between search options and visible columns

**Probable Causes to Investigate:**

1. **Different Column Source:**
   - Search dropdown may be using a different method to get available columns
   - Could be using `get_all_columns()` instead of `get_ordered_display_columns()`

2. **Toolbar Configuration:**
   - Toolbar may be configured independently of the main table view
   - Need to check how toolbar gets its column list

3. **Column Filtering Logic:**
   - Search dropdown may include columns that are available but not visible
   - Different filtering criteria between search and display

---

## Issue 3: Columns Dropdown Shows All Columns in Alphabetical Order

**Problem:**
- Columns dropdown (right side of toolbar) shows all columns alphabetically
- Includes system columns like "id" (possibly db_id)
- Includes "Unique Id" which may be duplicate/legacy column
- Not respecting FM-standard order or filtering

**Probable Causes to Investigate:**

1. **Using get_all_columns() Instead of Filtered Set:**
   - Columns dropdown likely calls `Columns.get_all_columns()` 
   - Should use `get_ordered_display_columns()` or similar filtered method

2. **No Column Filtering:**
   - System columns (db_system group) should not appear in user-facing dropdowns
   - Need to filter out internal columns like `id`, `hash`, etc.

3. **Alphabetical Sorting Override:**
   - Dropdown may be applying its own alphabetical sort
   - Need to ensure it respects the order from our ordering system

4. **Legacy Column Confusion:**
   - "id" column appearing suggests db_system columns are included
   - "Unique Id" vs "Source UID" confusion - need to clarify which should be user-visible

---

## Investigation Priority

### High Priority (Blocking Core Functionality)
1. **Issue 1** - Missing core columns prevents basic table functionality
2. **Column group verification** - Ensure core transaction columns are properly grouped

### Medium Priority (UX Consistency)  
3. **Issue 3** - Columns dropdown showing wrong columns
4. **Issue 2** - Search dropdown inconsistency

### Low Priority (Cleanup)
5. **Legacy column cleanup** - Resolve id/unique_id confusion

---

## Diagnostic Steps

### Step 1: Verify Column Groups
```python
# Check what's in each group
core_cols = Columns.get('core_transaction_cols')
user_cols = Columns.get('user_editable') 
display_cols = Columns.get_display_columns()
```

### Step 2: Check DataFrame Columns
```python
# In transaction_view_panel.py, log what columns are in the DataFrame
log.debug(f"DataFrame columns: {list(df.columns)}")
```

### Step 3: Trace Column Resolution
```python
# Add debug logging to get_ordered_display_columns()
ordered_cols = Columns.get_ordered_display_columns('categorize')
log.debug(f"Ordered display columns: {[col.db_name for col in ordered_cols]}")
```

### Step 4: Check Toolbar Column Sources
- Find where toolbar gets its column lists
- Verify if it's using the new ordering methods

---

## Expected Fixes

1. **Fix get_display_columns()** - Ensure it returns core + user columns
2. **Update toolbar column sources** - Use ordered, filtered column methods
3. **Filter system columns** - Exclude db_system group from user-facing dropdowns
4. **Apply consistent ordering** - Ensure all dropdowns use FM-standard order

---

*Created: 2025-07-17*
*Status: Investigation needed*
