from dataclasses import dataclass, field
from typing import Any, List

@dataclass(frozen=True)
class Column:
    """Represents a single, well-defined column in the application."""
    db_name: str         # The programmatic name (e.g., 'details')
    display_name: str    # The user-facing header (e.g., 'Details')
    dtype: Any
    groups: List[str] = field(default_factory=list)
    width: int = 10  # Default width for UI columns

    # Makes the object behave like its db_name by default, for clean internal use.
    # The `display_name` should be used explicitly at application boundaries (UI, exports).
    def __str__(self) -> str:
        return self.db_name

    def __hash__(self) -> int:
        return hash(self.db_name)

    def __eq__(self, other: Any) -> bool:
        if isinstance(other, Column):
            return self.db_name == other.db_name
        if isinstance(other, str):
            return self.db_name == other
        return NotImplemented
