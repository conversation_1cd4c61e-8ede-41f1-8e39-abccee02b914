---
type: "manual"
---

# Post-Refactoring Documentation Protocol

## Collaborative Process
- All changes and rationale are tracked in relevant `.md` files in # Post-Refactoring Documentation Protocol

## Collaborative Process
- All changes and rationale are tracked in relevant `.md` files.
- Major refactors require a summary and rationale in the appropriate discussion or architecture doc.

---

## Stepwise Documentation Flow

1. **Summarise the Refactor**
    - What was changed? (modules, files, APIs, structure)
    - Why was it changed? (goals, problems addressed)
    - Note any breaking changes or migration steps.

2. **Update Documentation**
    - Revise architecture, design, and feature docs to reflect new structure and APIs.
    - Update onboarding, usage guides, and code examples if affected.
    - Remove or clearly mark obsolete documentation.

3. **Changelog Entry**
    - Add a concise entry to the project changelog (if used) or summary section in the relevant `.md`.

4. **Review Inline Comments**
    - Ensure code comments and docstrings accurately reflect the new code.
    - Remove outdated or misleading comments.

5. **Record Discussion/Rationale**
    - If significant, add an entry to the discussion log or ADRs summarising:
        - The decision process
        - Alternatives considered (if any)
        - Final outcome

6. **Verification**
    - Confirm all documentation changes are committed alongside the code.
    - Optionally, have a peer review the updated docs for accuracy and clarity.

---

*Draft created 2025-07-18. Refine as needed for team use.*

- Major refactors require a summary and rationale in the appropriate discussion or architecture doc.

---

## Stepwise Documentation Flow

1. **Summarise the Refactor**
    - What was changed? (modules, files, APIs, structure)
    - Why was it changed? (goals, problems addressed)
    - Note any breaking changes or migration steps.

2. **Update Documentation**
    - Revise architecture, design, and feature docs to reflect new structure and APIs.
    - Update onboarding, usage guides, and code examples if affected.
    - Remove or clearly mark obsolete documentation.

3. **Changelog Entry**
    - Add a concise entry to the project changelog (if used) or summary section in the relevant `.md`.

4. **Review Inline Comments**
    - Ensure code comments and docstrings accurately reflect the new code.
    - Remove outdated or misleading comments.

5. **Record Discussion/Rationale**
    - If significant, add an entry to the discussion log or ADRs summarising:
        - The decision process
        - Alternatives considered (if any)
        - Final outcome

6. **Verification**
    - Confirm all documentation changes are committed alongside the code.
    - Optionally, have a peer review the updated docs for accuracy and clarity.

---

*Draft created 2025-07-18. Refine as needed for team use.*
